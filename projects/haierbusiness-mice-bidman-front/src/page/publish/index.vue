<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Button, RadioGroup, RadioButton } from 'ant-design-vue';
import Publish from '@haierbusiness-front/components/mice/pubilsh/index.vue';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
const open = ref(false); // 驳回弹窗是否打开
const viewSelect = ref('demand');
const router = useRouter();
const route = useRoute();
const demandJump = () => {
  router.push({
    path: '/bidman/publish/publishView',
    query: route.query,
  });
  console.log('需求发布');
};
const record = reactive({});
onMounted(() => {
  if (route.query.record.includes('%')) Object.assign(record, resolveParam(route.query.record));
  else {
    if (typeof route.query.record == 'string') Object.assign(record, JSON.parse(route.query.record));
  }
});
</script>
<template>
  <div class="container-push">
    <Publish :class="record.hideBtn == '1' ? 'footer-user-width' : ''">
      <template #footer>
        <div class="footer" v-if="record.hideBtn == '1'">
          <a-radio-group value="push">
            <a-radio-button @click="demandJump" value="demand">需求视图</a-radio-button>
            <a-radio-button value="push">发布视图</a-radio-button>
          </a-radio-group>
        </div>
      </template>
    </Publish>
  </div>
</template>
<style lang="less" scoped>
.container-push {
  height: 100%;
  background: #f1f2f6;
  padding: 0 auto;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
.footer {
  padding: 10px 20px;
  width: 1280px !important;
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  text-align: left;
  position: fixed;
  bottom: 0;
}
</style>
